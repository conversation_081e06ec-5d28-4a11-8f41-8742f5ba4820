@echo off
echo ========================================
echo BiblioTech Project Manual Cleanup
echo ========================================
echo.

echo Stopping any running BiblioTech processes...
taskkill /F /IM BiblioTech.exe 2>nul
taskkill /F /IM electron.exe 2>nul
echo Waiting for processes to fully terminate...
timeout /t 3 /nobreak >nul

echo.
echo Removing old build directories...

if exist "dist-electron" (
    echo Removing dist-electron...
    rmdir /S /Q "dist-electron" 2>nul
    if exist "dist-electron" echo WARNING: Could not remove dist-electron - files may be locked
)

if exist "dist-production" (
    echo Removing dist-production...
    rmdir /S /Q "dist-production" 2>nul
    if exist "dist-production" echo WARNING: Could not remove dist-production - files may be locked
)

if exist "dist-final" (
    echo Removing dist-final...
    rmdir /S /Q "dist-final" 2>nul
    if exist "dist-final" echo WARNING: Could not remove dist-final - files may be locked
)

if exist "dist-manual" (
    echo Removing dist-manual...
    rmdir /S /Q "dist-manual" 2>nul
    if exist "dist-manual" echo WARNING: Could not remove dist-manual - files may be locked
)

echo.
echo Removing old distribution folders...

if exist "BiblioTech-v1.0.1-Fixed-Windows" (
    echo Removing BiblioTech-v1.0.1-Fixed-Windows...
    rmdir /S /Q "BiblioTech-v1.0.1-Fixed-Windows" 2>nul
    if exist "BiblioTech-v1.0.1-Fixed-Windows" echo WARNING: Could not remove BiblioTech-v1.0.1-Fixed-Windows - files may be locked
)

if exist "dist-fixed" (
    echo Removing dist-fixed...
    rmdir /S /Q "dist-fixed" 2>nul
    if exist "dist-fixed" echo WARNING: Could not remove dist-fixed - files may be locked
)

if exist "BiblioTech-v1.0.0-Windows-Distribution" (
    echo Removing BiblioTech-v1.0.0-Windows-Distribution...
    rmdir /S /Q "BiblioTech-v1.0.0-Windows-Distribution" 2>nul
    if exist "BiblioTech-v1.0.0-Windows-Distribution" echo WARNING: Could not remove BiblioTech-v1.0.0-Windows-Distribution - files may be locked
)

if exist "BiblioTech-Portable" (
    echo Removing BiblioTech-Portable...
    rmdir /S /Q "BiblioTech-Portable" 2>nul
    if exist "BiblioTech-Portable" echo WARNING: Could not remove BiblioTech-Portable - files may be locked
)

echo.
echo Removing temporary files...
if exist "temp-empty" rmdir /S /Q "temp-empty" 2>nul

echo.
echo ========================================
echo Cleanup completed!
echo ========================================
echo.
echo If any directories could not be removed due to file locks:
echo 1. Restart your computer
echo 2. Run this script again
echo 3. Or manually delete the remaining folders
echo.
echo KEEP THESE ESSENTIAL ITEMS:
echo - src/ (source code)
echo - public/ (assets)
echo - node_modules/ (dependencies)
echo - BiblioTech-v1.0.2-Portable/ (WORKING EXECUTABLE)
echo - dist/ (current build output)
echo - package.json and other config files
echo - Documentation files (*.md)
echo.
pause
