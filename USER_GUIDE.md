# BiblioTech - Guide d'Utilisation

## Système de Gestion de Bibliothèque Moderne

### Installation et Démarrage

#### Option 1: Installation Complète
1. Exécutez `BiblioTech Setup 1.0.0.exe`
2. Su<PERSON><PERSON> l'assistant d'installation
3. Lancez l'application depuis le menu Démarrer ou le raccourci bureau

#### Option 2: Version Portable
1. Exécutez directement `BiblioTech-Portable-1.0.0.exe`
2. Aucune installation requise
3. Peut être utilisé depuis une clé USB

### Connexion

#### Comptes de Démonstration
- **Super Administrateur**
  - Nom d'utilisateur: `superadmin`
  - Mot de passe: `admin123`
  - Accès complet à toutes les fonctionnalités

- **Administrateur**
  - Nom d'utilisateur: `admin1`
  - Mot de passe: `admin123`
  - Accès limité (pas de gestion des administrateurs)

### Interface Principale

#### Navigation Latérale
- **Tableau de bord**: Vue d'ensemble des statistiques
- **Gestion des livres**: Ajou<PERSON>, modifier, supprimer des livres
- **Catégories**: Explorer les livres par catégorie
- **Utilisateurs**: Gérer les profils des emprunteurs
- **Suivi des prêts**: Suivre les livres empruntés
- **Gestion catégories**: Créer et organiser les catégories (Admin)
- **Gestion admin**: Gérer les comptes administrateurs (Super Admin)
- **Journaux d'audit**: Historique des actions (Super Admin)

### Fonctionnalités Principales

#### 1. Gestion des Livres

**Ajouter un Livre**
1. Allez dans "Gestion des livres"
2. Remplissez le formulaire:
   - Titre (requis)
   - Auteur (requis)
   - Catégorie (requis)
   - Genre (requis)
   - ISBN (optionnel)
   - Année de publication (optionnel)
   - Description (optionnel)
3. Cliquez "Ajouter le livre"

**Import en Lot**
1. Cliquez "Import en lot" dans la section livres
2. Téléchargez le modèle CSV fourni
3. Remplissez le fichier avec vos données
4. Glissez-déposez ou sélectionnez votre fichier
5. Vérifiez l'aperçu des données
6. Confirmez l'importation

**Recherche et Filtrage**
- Utilisez la barre de recherche pour trouver des livres
- Filtrez par statut (disponible/emprunté)
- Triez les colonnes en cliquant sur les en-têtes

#### 2. Gestion des Catégories

**Créer une Catégorie**
1. Allez dans "Gestion catégories"
2. Cliquez "Nouvelle catégorie"
3. Saisissez le nom et la description
4. Cliquez "Créer"

**Réorganiser les Catégories**
- Glissez-déposez les catégories pour les réorganiser
- L'ordre sera mis à jour automatiquement

#### 3. Gestion des Utilisateurs

**Ajouter un Utilisateur**
1. Allez dans "Utilisateurs"
2. Cliquez "Nouvel utilisateur"
3. Remplissez les informations
4. Sauvegardez

**Rechercher des Utilisateurs**
- Utilisez la barre de recherche
- Cliquez sur une carte utilisateur pour plus de détails

#### 4. Suivi des Prêts

**Marquer un Livre comme Emprunté**
1. Dans la liste des livres, cliquez l'icône de statut
2. Le livre passe automatiquement en "Emprunté"

**Consulter les Prêts**
- Allez dans "Suivi des prêts"
- Voyez les statistiques en temps réel
- Identifiez les livres en retard (badge rouge)
- Voyez les prêts à échéance proche (badge orange)

#### 5. Gestion Administrative (Super Admin)

**Créer un Administrateur**
1. Allez dans "Gestion admin"
2. Cliquez "Nouvel administrateur"
3. Remplissez les informations
4. Définissez un mot de passe sécurisé

**Consulter les Journaux d'Audit**
1. Allez dans "Journaux d'audit"
2. Filtrez par action, ressource ou date
3. Exportez les logs en CSV si nécessaire

### Raccourcis Clavier

#### Navigation Rapide
- `Ctrl + 1`: Tableau de bord
- `Ctrl + 2`: Gestion des livres
- `Ctrl + 3`: Catégories
- `Ctrl + 4`: Utilisateurs
- `Ctrl + 5`: Suivi des prêts

#### Actions Rapides
- `Ctrl + N`: Nouveau livre
- `Ctrl + I`: Import en lot
- `Ctrl + E`: Exporter les données
- `Ctrl + R`: Actualiser la page
- `F11`: Plein écran

### Fonctionnalités Avancées

#### Export de Données
- Exportez la liste des livres en CSV
- Exportez les journaux d'audit
- Utilisez les données dans Excel ou autres outils

#### Recherche Avancée
- Recherche dans tous les champs (titre, auteur, catégorie, genre)
- Filtrage par statut de disponibilité
- Tri par n'importe quelle colonne

#### Validation des Données
- Validation automatique des formulaires
- Messages d'erreur explicites
- Prévention des doublons lors de l'import

### Sécurité et Permissions

#### Niveaux d'Accès
- **Super Admin**: Accès complet, gestion des administrateurs
- **Administrateur**: Gestion des livres, catégories et utilisateurs

#### Audit et Traçabilité
- Toutes les actions sont enregistrées
- Horodatage précis de chaque modification
- Identification de l'utilisateur responsable

#### Session et Sécurité
- Déconnexion automatique après 30 minutes d'inactivité
- Validation des données côté client
- Protection contre les injections

### Dépannage

#### Problèmes Courants

**L'application ne démarre pas**
- Vérifiez que Windows Defender n'bloque pas l'exécutable
- Essayez d'exécuter en tant qu'administrateur
- Redémarrez votre ordinateur

**Données perdues**
- Les données de démonstration sont en mémoire
- Elles sont réinitialisées à chaque redémarrage
- Pour une utilisation réelle, une base de données persistante serait nécessaire

**Performance lente**
- Fermez d'autres applications gourmandes en mémoire
- Vérifiez l'espace disque disponible
- Redémarrez l'application

#### Support Technique

Cette application est une démonstration et fonctionne entièrement hors ligne.
Aucune connexion internet n'est requise.

### Caractéristiques Techniques

- **Plateforme**: Windows 10/11 (64-bit)
- **Taille**: ~150-200 MB
- **Mémoire**: 4 GB RAM recommandés
- **Stockage**: Fonctionne en mémoire (données temporaires)
- **Réseau**: Aucune connexion requise

### Conclusion

BiblioTech offre une solution moderne et intuitive pour la gestion de bibliothèque avec:
- Interface utilisateur moderne et responsive
- Fonctionnalités complètes de gestion
- Sécurité et audit intégrés
- Facilité d'utilisation
- Performance optimisée pour desktop

Cette démonstration illustre les capacités d'un système de gestion de bibliothèque professionnel adapté aux besoins modernes.
