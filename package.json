{"name": "bibliotech", "private": true, "version": "1.0.2", "description": "Système de gestion de bibliothèque moderne - BiblioTech", "author": {"name": "BiblioTech Team", "email": "<EMAIL>"}, "type": "module", "main": "public/electron.cjs", "homepage": "./", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && electron .\"", "build-electron": "npm run build && electron .", "dist": "npm run build && electron-builder", "dist-win": "npm run build && electron-builder --win", "dist-portable": "npm run build && electron-builder --win portable", "dist-win-all": "npm run build && electron-builder --win --x64 --ia32", "clean": "rimraf dist dist-electron node_modules/.cache", "rebuild": "npm run clean && npm install && npm run dist-win-all"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "electron": "^36.4.0", "electron-builder": "^26.0.12", "electron-is-dev": "^3.0.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rimraf": "^6.0.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "wait-on": "^8.0.3"}, "build": {"appId": "com.bibliotech.app", "productName": "BiblioTech", "copyright": "Copyright © 2025 BiblioTech Team", "directories": {"output": "dist-production"}, "files": ["dist/**/*", "public/electron.cjs", "public/assets/**/*", "public/usersimages/**/*", "node_modules/**/*", "!node_modules/.cache", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": [{"target": "portable", "arch": ["x64"]}], "icon": "public/assets/icon.png", "requestedExecutionLevel": "asInvoker", "verifyUpdateCodeSignature": false, "artifactName": "BiblioTech-Setup-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "BiblioTech", "uninstallDisplayName": "BiblioTech - Système de gestion de bibliothèque", "installerIcon": "public/assets/icon.png", "uninstallerIcon": "public/assets/icon.png", "installerHeaderIcon": "public/assets/icon.png", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Office"}, "portable": {"artifactName": "BiblioTech-Portable-${version}-${arch}.${ext}", "requestExecutionLevel": "user"}, "extraResources": [{"from": "public/assets", "to": "assets"}, {"from": "public/usersimages", "to": "usersimages"}], "compression": "maximum", "npmRebuild": false, "nodeGypRebuild": false}}