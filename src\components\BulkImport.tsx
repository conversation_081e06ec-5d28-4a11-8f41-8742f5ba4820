import React, { useState, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { DownloadIcon, XIcon, CheckIcon } from './Icons';
import type { ImportResult, ImportPreview, BookImportData } from '../types/library';
import { validateBookData, sanitizeBookData } from '../types/library';

interface BulkImportProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (books: BookImportData[]) => Promise<ImportResult>;
  categories: string[];
}

const BulkImport: React.FC<BulkImportProps> = ({ isOpen, onClose, onImport }) => {
  const [step, setStep] = useState<'upload' | 'preview' | 'importing' | 'results'>('upload');
  const [, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { logAction } = useAuth();

  if (!isOpen) return null;

  const handleFileSelect = (selectedFile: File) => {
    if (!selectedFile) return;

    const validTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (!validTypes.includes(selectedFile.type) && !selectedFile.name.match(/\.(csv|xlsx|xls)$/i)) {
      alert('Format de fichier non supporté. Veuillez utiliser CSV ou Excel (.xlsx, .xls)');
      return;
    }

    setFile(selectedFile);
    parseFile(selectedFile);
  };

  const parseFile = async (file: File) => {
    try {
      const text = await file.text();
      const lines = text.split('\n').filter(line => line.trim());
      
      if (lines.length < 2) {
        alert('Le fichier doit contenir au moins une ligne d\'en-tête et une ligne de données');
        return;
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      const data: any[][] = [];
      const errors: any[] = [];
      let validRows = 0;

      // Expected headers mapping
      const headerMapping: Record<string, string> = {
        'titre': 'titre',
        'title': 'titre',
        'auteur': 'auteur',
        'author': 'auteur',
        'categorie': 'categorie',
        'category': 'categorie',
        'genre': 'genre',
        'isbn': 'isbn',
        'annee': 'anneePublication',
        'year': 'anneePublication',
        'description': 'description'
      };

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
        const rowData: any = {};

        headers.forEach((header, index) => {
          const mappedHeader = headerMapping[header.toLowerCase()];
          if (mappedHeader && values[index]) {
            rowData[mappedHeader] = values[index];
          }
        });

        // Convert year to number if present
        if (rowData.anneePublication) {
          rowData.anneePublication = parseInt(rowData.anneePublication);
        }

        const rowErrors = validateBookData(rowData);
        if (rowErrors.length > 0) {
          errors.push(...rowErrors.map(err => ({ ...err, row: i })));
        } else {
          validRows++;
        }

        data.push(values);
      }

      setPreview({
        headers,
        data,
        validRows,
        invalidRows: data.length - validRows,
        errors
      });

      setStep('preview');
    } catch (error) {
      alert('Erreur lors de la lecture du fichier');
    }
  };

  const handleImport = async () => {
    if (!preview) return;

    setStep('importing');
    setProgress(0);

    const books: BookImportData[] = [];
    const progressIncrement = 100 / preview.data.length;

    for (let i = 0; i < preview.data.length; i++) {
      const values = preview.data[i];
      const rowData: any = {};

      preview.headers.forEach((header, index) => {
        const headerMapping: Record<string, string> = {
          'titre': 'titre',
          'title': 'titre',
          'auteur': 'auteur',
          'author': 'auteur',
          'categorie': 'categorie',
          'category': 'categorie',
          'genre': 'genre',
          'isbn': 'isbn',
          'annee': 'anneePublication',
          'year': 'anneePublication',
          'description': 'description'
        };

        const mappedHeader = headerMapping[header.toLowerCase()];
        if (mappedHeader && values[index]) {
          rowData[mappedHeader] = values[index];
        }
      });

      if (rowData.anneePublication) {
        rowData.anneePublication = parseInt(rowData.anneePublication);
      }

      const errors = validateBookData(rowData);
      if (errors.length === 0) {
        books.push(sanitizeBookData(rowData));
      }

      setProgress((i + 1) * progressIncrement);
      await new Promise(resolve => setTimeout(resolve, 10)); // Simulate processing time
    }

    try {
      const result = await onImport(books);
      setImportResult(result);
      setStep('results');
      
      logAction('BULK_IMPORT', 'BOOKS', undefined, 
        `Import de ${result.success} livres (${result.errors} erreurs, ${result.duplicates} doublons)`);
    } catch (error) {
      alert('Erreur lors de l\'importation');
      setStep('preview');
    }
  };

  const downloadTemplate = () => {
    const template = [
      ['titre', 'auteur', 'categorie', 'genre', 'isbn', 'annee', 'description'],
      ['Le Petit Prince', 'Antoine de Saint-Exupéry', 'Fiction', 'Conte', '9782070408504', '1943', 'Un conte poétique et philosophique'],
      ['1984', 'George Orwell', 'Fiction', 'Dystopie', '9782070368228', '1949', 'Roman dystopique sur la surveillance']
    ];

    const csvContent = template.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template_import_livres.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const resetImport = () => {
    setStep('upload');
    setFile(null);
    setPreview(null);
    setImportResult(null);
    setProgress(0);
  };

  return (
    <div className="modal-overlay">
      <div className="modal">
        <div className="modal-header">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="modal-title">Import en lot de livres</h2>
              <p className="modal-subtitle">
                {step === 'upload' && 'Sélectionnez un fichier CSV ou Excel'}
                {step === 'preview' && 'Vérifiez les données avant l\'import'}
                {step === 'importing' && 'Import en cours...'}
                {step === 'results' && 'Résultats de l\'import'}
              </p>
            </div>
            <button onClick={onClose} className="icon-button">
              <XIcon size={20} />
            </button>
          </div>
        </div>

        <div className="modal-content">
          {step === 'upload' && (
            <div>
              <div className="mb-6">
                <button onClick={downloadTemplate} className="btn btn-secondary">
                  <DownloadIcon size={16} />
                  Télécharger le modèle
                </button>
              </div>

              <div
                className={`file-upload-area ${isDragOver ? 'dragover' : ''}`}
                onDragOver={(e) => {
                  e.preventDefault();
                  setIsDragOver(true);
                }}
                onDragLeave={() => setIsDragOver(false)}
                onDrop={(e) => {
                  e.preventDefault();
                  setIsDragOver(false);
                  const files = e.dataTransfer.files;
                  if (files.length > 0) {
                    handleFileSelect(files[0]);
                  }
                }}
                onClick={() => fileInputRef.current?.click()}
              >
                <div className="file-upload-icon">📁</div>
                <div className="file-upload-text">
                  Glissez-déposez votre fichier ici ou cliquez pour sélectionner
                </div>
                <div className="file-upload-hint">
                  Formats supportés: CSV, Excel (.xlsx, .xls)
                </div>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileSelect(file);
                }}
                style={{ display: 'none' }}
              />
            </div>
          )}

          {step === 'preview' && preview && (
            <div>
              <div className="mb-6">
                <div className="stats-grid">
                  <div className="stat-card">
                    <div className="stat-value">{preview.data.length}</div>
                    <div className="stat-label">Total lignes</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-value text-success">{preview.validRows}</div>
                    <div className="stat-label">Valides</div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-value text-error">{preview.invalidRows}</div>
                    <div className="stat-label">Erreurs</div>
                  </div>
                </div>
              </div>

              <div className="preview-table-container">
                <table className="preview-table">
                  <thead>
                    <tr>
                      <th>Ligne</th>
                      {preview.headers.map((header, index) => (
                        <th key={index}>{header}</th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {preview.data.slice(0, 10).map((row, index) => {
                      const hasError = preview.errors.some(err => err.row === index + 2);
                      return (
                        <tr key={index} className={hasError ? 'error-row' : ''}>
                          <td>{index + 2}</td>
                          {row.map((cell, cellIndex) => (
                            <td key={cellIndex} className={hasError ? 'error-cell' : ''}>
                              {cell}
                            </td>
                          ))}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {preview.errors.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-semibold mb-2 text-error">Erreurs détectées :</h4>
                  <div className="max-h-32 overflow-y-auto">
                    {preview.errors.slice(0, 5).map((error, index) => (
                      <div key={index} className="text-sm text-error mb-1">
                        Ligne {error.row}: {error.message}
                      </div>
                    ))}
                    {preview.errors.length > 5 && (
                      <div className="text-sm text-neutral">
                        ... et {preview.errors.length - 5} autres erreurs
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {step === 'importing' && (
            <div className="text-center">
              <div className="mb-4">
                <div className="text-lg font-semibold">Import en cours...</div>
                <div className="text-neutral">Veuillez patienter</div>
              </div>
              <div className="progress-bar">
                <div className="progress-fill" style={{ width: `${progress}%` }}></div>
              </div>
              <div className="mt-2 text-sm text-neutral">{Math.round(progress)}%</div>
            </div>
          )}

          {step === 'results' && importResult && (
            <div>
              <div className="stats-grid mb-6">
                <div className="stat-card">
                  <div className="stat-value text-success">{importResult.success}</div>
                  <div className="stat-label">Importés</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value text-error">{importResult.errors}</div>
                  <div className="stat-label">Erreurs</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value text-warning">{importResult.duplicates}</div>
                  <div className="stat-label">Doublons</div>
                </div>
              </div>

              <div className="text-center">
                <CheckIcon size={48} className="text-success mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Import terminé !</h3>
                <p className="text-neutral">
                  {importResult.success} livre(s) ont été importés avec succès.
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          {step === 'upload' && (
            <button onClick={onClose} className="btn btn-secondary">
              Annuler
            </button>
          )}

          {step === 'preview' && (
            <>
              <button onClick={resetImport} className="btn btn-secondary">
                Retour
              </button>
              <button 
                onClick={handleImport} 
                className="btn btn-primary"
                disabled={preview?.validRows === 0}
              >
                Importer {preview?.validRows} livre(s)
              </button>
            </>
          )}

          {step === 'results' && (
            <>
              <button onClick={resetImport} className="btn btn-secondary">
                Nouvel import
              </button>
              <button onClick={onClose} className="btn btn-primary">
                Fermer
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkImport;
