// Composant pour ajouter un nouveau livre
import React, { useState } from 'react';
import { PlusIcon } from './Icons';

interface Props {
  onAdd: (livre: {
    titre: string;
    auteur: string;
    categorie: string;
    genre: string;
  }) => void;
  categories: string[];
}

const LivreForm: React.FC<Props> = ({ onAdd, categories }) => {
  const [titre, setTitre] = useState('');
  const [auteur, setAuteur] = useState('');
  const [categorie, setCategorie] = useState(categories[0] || '');
  const [genre, setGenre] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!titre || !auteur || !categorie || !genre) return;

    setIsSubmitting(true);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    onAdd({ titre, auteur, categorie, genre });
    setTitre('');
    setAuteur('');
    setCategorie(categories[0] || '');
    setGenre('');
    setIsSubmitting(false);
  };

  return (
    <div className="card mb-8">
      <div className="card-header">
        <h2 className="card-title">Ajouter un nouveau livre</h2>
        <p className="card-subtitle">Enregistrez un nouveau livre dans votre collection</p>
      </div>
      <div className="card-content">
        <form onSubmit={handleSubmit}>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="titre" className="form-label">Titre *</label>
              <input
                id="titre"
                type="text"
                className="form-input"
                placeholder="Entrez le titre du livre"
                value={titre}
                onChange={e => setTitre(e.target.value)}
                required
                disabled={isSubmitting}
              />
            </div>
            <div className="form-group">
              <label htmlFor="auteur" className="form-label">Auteur *</label>
              <input
                id="auteur"
                type="text"
                className="form-input"
                placeholder="Nom de l'auteur"
                value={auteur}
                onChange={e => setAuteur(e.target.value)}
                required
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="categorie" className="form-label">Catégorie *</label>
              <select
                id="categorie"
                className="form-select"
                value={categorie}
                onChange={e => setCategorie(e.target.value)}
                required
                disabled={isSubmitting}
              >
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>
            <div className="form-group">
              <label htmlFor="genre" className="form-label">Genre *</label>
              <input
                id="genre"
                type="text"
                className="form-input"
                placeholder="Genre littéraire"
                value={genre}
                onChange={e => setGenre(e.target.value)}
                required
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className="form-actions">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSubmitting || !titre || !auteur || !categorie || !genre}
            >
              <PlusIcon size={16} />
              {isSubmitting ? 'Ajout en cours...' : 'Ajouter le livre'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LivreForm;
